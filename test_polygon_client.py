#!/usr/bin/env python3
"""
Simple test script to demonstrate Polygon.io data fetching
"""

import requests
import json
from datetime import datetime, timedelta

# Your Polygon.io API key
POLYGON_API_KEY = "********************************"
BASE_URL = "https://api.polygon.io"

def test_market_status():
    """Test market status endpoint"""
    print("🏛️  Testing Market Status...")
    try:
        url = f"{BASE_URL}/v1/marketstatus/now"
        params = {"apikey": POLYGON_API_KEY}
        
        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Market Status: {data.get('market', 'Unknown')}")
            return True
        else:
            print(f"❌ Error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_previous_close(symbol):
    """Test previous close price endpoint"""
    print(f"💰 Testing Previous Close for {symbol}...")
    try:
        url = f"{BASE_URL}/v2/aggs/ticker/{symbol}/prev"
        params = {"apikey": POLYGON_API_KEY}
        
        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "OK" and "results" in data and data["results"]:
                result = data["results"][0]
                close_price = result.get("c", 0)
                open_price = result.get("o", 0)
                high_price = result.get("h", 0)
                low_price = result.get("l", 0)
                volume = result.get("v", 0)
                timestamp = result.get("t", 0)
                
                if timestamp:
                    dt = datetime.fromtimestamp(timestamp / 1000)
                    date_str = dt.strftime("%Y-%m-%d")
                else:
                    date_str = "N/A"
                
                print(f"✅ {symbol}: Close ${close_price:.2f} | O: ${open_price:.2f} | H: ${high_price:.2f} | L: ${low_price:.2f} | V: {volume:,} ({date_str})")
                return True
            else:
                print(f"❌ No data available for {symbol}")
                return False
        else:
            print(f"❌ Error: {response.status_code}")
            if response.status_code == 429:
                print("   Rate limit exceeded - wait a moment before trying again")
            return False
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_daily_bars(symbol):
    """Test daily bars endpoint"""
    print(f"📊 Testing Daily Bars for {symbol}...")
    try:
        yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
        url = f"{BASE_URL}/v2/aggs/ticker/{symbol}/range/1/day/{yesterday}/{yesterday}"
        params = {"apikey": POLYGON_API_KEY}
        
        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "OK" and "results" in data and data["results"]:
                result = data["results"][0]
                open_price = result.get("o", 0)
                close_price = result.get("c", 0)
                high_price = result.get("h", 0)
                low_price = result.get("l", 0)
                volume = result.get("v", 0)
                
                print(f"✅ {symbol} Daily - O: ${open_price:.2f}, C: ${close_price:.2f}, H: ${high_price:.2f}, L: ${low_price:.2f}, V: {volume:,}")
                return True
            else:
                print(f"❌ No daily data available for {symbol}")
                return False
        else:
            print(f"❌ Error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing Polygon.io API Access...")
    print("=" * 50)
    
    # Test market status
    test_market_status()
    print()
    
    # Test symbols
    symbols = ["AAPL", "MSFT", "GOOGL"]
    
    for symbol in symbols:
        print(f"Testing {symbol}...")
        test_previous_close(symbol)
        print()
        
        # Add delay to avoid rate limiting
        import time
        time.sleep(2)
    
    print("=" * 50)
    print("✅ Testing completed!")
    print("\n📝 Notes:")
    print("- If you see 403 errors, your API key might not have access to that endpoint")
    print("- If you see 429 errors, you're hitting rate limits - increase delays between requests")
    print("- Market data is typically delayed by 15 minutes for free/basic plans")
    print("- Real-time data requires a premium subscription")

if __name__ == "__main__":
    main()
