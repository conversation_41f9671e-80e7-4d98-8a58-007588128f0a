# import os
# import django

# os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
# django.setup()
# from django.utils import timezone
# from Authentication.models import User
# from Project.models import *
# import base64
# import datetime
# from flask import Flask, request
# from flask_socketio import SocketIO, emit, join_room
# from flask_cors import CORS
# from flask import request


# app = Flask(__name__)
# app.config['SECRET_KEY'] = 'your_secret_key'
# app.config['UPLOAD_FOLDER'] = os.path.join('media', 'prepare_chat_files')

# CORS(app, resources={r"/*": {"origins": "*"}})  # Allow firecamp.dev
# socketio = SocketIO(app, cors_allowed_origins=["https://firecamp.dev","http://localhost:3000","http://localhost:30044","https://room8.flexioninfotech.com"], max_http_buffer_size=1024*1024*400, ping_timeout=120, ping_interval=25)



# # @socketio.on('join_socket')
# # def join_socket(data):
# #     try:
# #         auth_token = data.get('Authorization')
# #         if not auth_token:
# #             emit('join_socket', {'message': 'JWT Token Required'})
# #             return
# #         user_id = decode_token(f'Bearer {auth_token}')
# #         if not user_id:
# #             emit('join_socket', {'message': 'Invalid JWT Token'})
# #             return
# #         user_socket_id = request.sid
# #         user_data = User.objects.get(pk=user_id)
# #         user_data.socket_id = user_socket_id
# #         user_data.save()
# #         room = str(user_id)
# #         if not user_socket_id:
# #             emit('join_socket', {'message': 'User not connected'})
# #             return

# #         # Join the user to the room
# #         join_room(room)

# #         # Emit a success message to the user
# #         emit('join_socket', {'message': f'Joined room {room}'}, room=room)
# #         print(user_socket_id)
# #     except Exception as e:
# #         emit("join_socket", {'status': False, 'error': str(e)}), 500
# #         return


# # @socketio.on('send_message')
# # def send_message(data):
# #     try:
# #         auth_token = data.get('Authorization')
# #         message = data.get('message', '')
# #         type = data.get('type', 'text')
# #         message_id = data.get('message_id', '')
# #         file = data.get('file', '')
# #         to_user_id = data.get('to')
# #         if not auth_token:
# #             emit('send_message', {'message': 'JWT Token Required'})
# #             return
# #         user_id = decode_token(f'Bearer {auth_token}')
# #         if not user_id:
# #             emit('send_message', {'message': 'Invalid JWT Token'})
# #             return
# #         room = str(to_user_id)
# #         print(f'room --> {room}')
# #         if type == 'reply_message':
# #             if message_id is None or message_id == '':
# #                 emit('send_message', {
# #                      'message': 'To reply to a message Id is required'})
# #                 return
# #             else:
# #                 create = ChatMessage.objects.create(
# #                     from_user_id=user_id,
# #                     to_user_id=to_user_id,
# #                     type=type,
# #                     message_id=message_id,
# #                     messages=message
# #                 )

# #                 noti_data = {
# #                         'user_id': create.to_user.pk,
# #                         'type': 'message',
# #                         'title': 'Room8',
# #                         'message': f'{create.from_user.name} Messaged You',
# #                         'messages': message,
# #                         "from":create.from_user.pk
# #                     }
# #                 socketio.emit('receive_message', {'id': create.pk, 'message': message, 'type': type,
# #                               'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id}, to=room)
# #                 emit('send_message', {'id': create.pk, 'message': message, 'type': type,
# #                      'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id})
# #                 send_in_app_notification(
# #                     noti_data, User, socketio.emit)
# #                 return
# #         if type in ['image', 'voice', 'custom']:
# #             if file is None or file == '':
# #                 emit('send_message', {
# #                      'message': 'For type file or voice a file is required'})
# #                 return
# #             else:
# #                 file_path = None
# #                 file_path = save_base64_file(
# #                     file, user_id, datetime.datetime.now())
# #                 create = ChatMessage.objects.create(
# #                     from_user_id=user_id,
# #                     to_user_id=to_user_id,
# #                     type=type,
# #                     file=file_path,
# #                     message_id=message_id,
# #                     messages=f'Sent you an {type}'
# #                 )
# #                 noti_data = {
# #                         'user_id': create.to_user.pk,
# #                         'type': 'message',
# #                         'title': 'Room8',
# #                         'message': f'{create.from_user.name} Sent You A File',
# #                         'messages': message,
# #                         "from":create.from_user.pk
# #                     }
# #                 socketio.emit('receive_message', {'id': create.pk, 'message': file_path, 'type': type,
# #                               'file': file_path, 'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id}, to=room)
# #                 emit('send_message', {'id': create.pk, 'message': file_path, 'type': type,
# #                      'file': file_path, 'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id})
# #                 send_in_app_notification(
# #                     noti_data, User, socketio.emit)
# #                 return
# #         create = ChatMessage.objects.create(
# #             from_user_id=user_id,
# #             to_user_id=to_user_id,
# #             type=type,
# #             messages=message
# #         )
# #         noti_data = {
# #                         'user_id': create.to_user.pk,
# #                         'type': 'message',
# #                         'title': 'Room8',
# #                         'message': f'{create.from_user.name} Messaged You',
# #                         'messages': message,
# #                         "from":create.from_user.pk
# #                     }
# #         socketio.emit('receive_message', {'id': create.pk, 'message': message, 'type': type,
# #                       'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id}, to=room)

# #         emit('send_message', {'id': create.pk, 'message': message, 'type': type,
# #              'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id})
# #         send_in_app_notification(
# #                     noti_data, User, socketio.emit)
# #     except Exception as e:
# #         emit("send_message", {'status': False, 'error': str(e)}), 500
# #         return


# # @socketio.on('delete_message')
# # def delete_message(data):
# #     auth_token = data.get('Authorization')
# #     message_id = data.get('message_id')
# #     if not auth_token:
# #         emit('delete_message', {'message': 'JWT Token Required'})
# #         return
# #     user_id = decode_token(f'Bearer {auth_token}')
# #     if not user_id:
# #         emit('delete_message', {'message': 'Invalid JWT Token'})
# #         return
# #     try:
# #         message = ChatMessage.objects.get(pk=message_id)
# #         to_user_id = message.to_user_id
# #         room = str(to_user_id)
# #         message.delete()
# #         socketio.emit('delete_message', {
# #             'status': True,
# #             'message': 'Message deleted successfully',
# #             'message_id': message_id,
# #             'to_user_id': to_user_id
# #         }, to=room)
# #         emit('delete_message', {
# #             'status': True,
# #             'message': 'Message deleted successfully',
# #             'message_id': message_id,
# #             'to_user_id': to_user_id
# #         })
# #     except ChatMessage.DoesNotExist:
# #         emit('delete_message', {'status': False,'message': 'Message not found'})


# # @socketio.on('is_typing')
# # def is_typing(data):
# #     auth_token = data.get('Authorization')
# #     to_room = data.get('to')
# #     is_typing = data.get('is_typing')
# #     if not auth_token:
# #         emit('is_typing', {'message': 'JWT Token Required'})
# #         return
# #     user_id = decode_token(f'Bearer {auth_token}')
# #     if not user_id:
# #         emit('is_typing', {'message': 'Invalid JWT Token'})
# #         return
# #     room = str(to_room)
# #     if is_typing == '1':
# #         socketio.emit('is_typing', {'is_typing': True , 'to':to_room ,'user_id':user_id}, to=room)
# #         socketio.emit('is_typing_list', {'is_typing': True , 'to':to_room ,'user_id':user_id}, to=room)
# #     else:
# #         socketio.emit('is_typing', {'is_typing': False, 'to':to_room ,'user_id':user_id}, to=room)
# #         socketio.emit('is_typing_list', {'is_typing': False, 'to':to_room ,'user_id':user_id}, to=room)


# # @socketio.on('message_read')
# # def is_typing(data):
# #     auth_token = data.get('Authorization')
# #     to_room = data.get('to')
# #     message_id = data.get('message_id')
# #     if not auth_token:
# #         emit('message_read', {'message': 'JWT Token Required'})
# #         return
# #     user_id = decode_token(f'Bearer {auth_token}')
# #     if not user_id:
# #         emit('message_read', {'message': 'Invalid JWT Token'})
# #         return
# #     if not message_id:
# #         emit('message_read', {'message': 'message_id field is required'})
# #         return

# #     room = str(to_room)

# #     try:
# #         message = ChatMessage.objects.get(pk=message_id)
# #         message.is_read = True
# #         message.read_time = datetime.datetime.now()
# #         message.save()
# #         socketio.emit('message_read', {
# #                       'id': message.pk, 'is_read': True, 'read_time': message.read_time}, to=room)

# #     except ChatMessage.DoesNotExist:
# #         emit('message_read', {'message': 'Chat message not found'})


# if __name__ == '__main__':
#     socketio.run(app, allow_unsafe_werkzeug=True, debug=True, port=30044)

















import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()
from django.utils import timezone

from Project.models import *
import base64
import datetime
import requests
import json
import threading
import time
from datetime import datetime, timedelta
from flask import Flask, request
from flask_socketio import SocketIO, emit, join_room
from flask_cors import CORS
from flask import request

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key'
app.config['UPLOAD_FOLDER'] = os.path.join('media', 'prepare_chat_files')

CORS(app, resources={r"/*": {"origins": "*"}})
socketio = SocketIO(app, cors_allowed_origins=["https://firecamp.dev","http://localhost:3000","http://localhost:30044","https://room8.flexioninfotech.com"], max_http_buffer_size=1024*1024*400, ping_timeout=120, ping_interval=25)

# Polygon.io configuration - Use delayed feed (15-minute delay, no special subscription needed)
POLYGON_API_KEY = "********************************"
POLYGON_WS_URL = "wss://delayed.polygon.io/stocks"  # Delayed feed (15-minute delay)

# Global WebSocket connection
polygon_ws = None

class PolygonDataClient:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://api.polygon.io"
        self.is_running = False
        self.subscribed_symbols = ["AAPL", "MSFT", "GOOGL", "TSLA", "NVDA"]
        self.polling_thread = None

    def get_current_price(self, symbol):
        """Get current price for a symbol using REST API"""
        try:
            # Use previous close price endpoint which should work with basic API access
            url = f"{self.base_url}/v2/aggs/ticker/{symbol}/prev"
            params = {"apikey": self.api_key}

            response = requests.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                print(f"get_current_price data --> {data}")
                if data.get("status") == "OK" and "results" in data and data["results"]:
                    result = data["results"][0]
                    close_price = result.get("c", 0)
                    open_price = result.get("o", 0)
                    high_price = result.get("h", 0)
                    low_price = result.get("l", 0)
                    volume = result.get("v", 0)
                    timestamp = result.get("t", 0)

                    # Convert timestamp to readable format
                    if timestamp:
                        dt = datetime.fromtimestamp(timestamp / 1000)
                        date_str = dt.strftime("%Y-%m-%d")
                    else:
                        date_str = "N/A"

                    print(f"💰 {symbol}: Close ${close_price:.2f} | O: ${open_price:.2f} | H: ${high_price:.2f} | L: ${low_price:.2f} | V: {volume:,} ({date_str})")
                    return {
                        "symbol": symbol,
                        "close": close_price,
                        "open": open_price,
                        "high": high_price,
                        "low": low_price,
                        "volume": volume,
                        "timestamp": timestamp
                    }
            else:
                print(f"❌ Error fetching {symbol}: {response.status_code}")

        except Exception as e:
            print(f"❌ Error fetching price for {symbol}: {e}")
        return None

    def get_daily_bars(self, symbol):
        """Get daily aggregate bars for a symbol"""
        try:
            # Get yesterday's date for the most recent complete data
            yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
            url = f"{self.base_url}/v2/aggs/ticker/{symbol}/range/1/day/{yesterday}/{yesterday}"
            params = {"apikey": self.api_key}

            response = requests.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "OK" and "results" in data and data["results"]:
                    result = data["results"][0]
                    open_price = result.get("o", 0)
                    close_price = result.get("c", 0)
                    high_price = result.get("h", 0)
                    low_price = result.get("l", 0)
                    volume = result.get("v", 0)

                    print(f"📈 {symbol} Daily - O: ${open_price:.2f}, C: ${close_price:.2f}, H: ${high_price:.2f}, L: ${low_price:.2f}, V: {volume:,}")
                    return {
                        "symbol": symbol,
                        "open": open_price,
                        "close": close_price,
                        "high": high_price,
                        "low": low_price,
                        "volume": volume
                    }

        except Exception as e:
            print(f"❌ Error fetching daily bars for {symbol}: {e}")
        return None

    def get_market_status(self):
        """Get current market status"""
        try:
            url = f"{self.base_url}/v1/marketstatus/now"
            params = {"apikey": self.api_key}

            response = requests.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                if "market" in data:
                    market_status = data["market"]
                    print(f"🏛️  Market Status: {market_status}")
                    return market_status

        except Exception as e:
            print(f"❌ Error fetching market status: {e}")
        return None

    def polling_loop(self):
        """Main polling loop to fetch data periodically"""
        print("� Starting data polling loop...")

        while self.is_running:
            try:
                print(f"\n� === Polygon.io Data Update - {datetime.now().strftime('%H:%M:%S')} ===")

                # Get market status first
                self.get_market_status()

                # Fetch current prices for all subscribed symbols
                for symbol in self.subscribed_symbols:
                    self.get_current_price(symbol)
                    time.sleep(2)  # Increased delay between requests to avoid rate limiting

                print("=" * 60)

                # Wait for next update (60 seconds)
                time.sleep(60)

            except Exception as e:
                print(f"❌ Error in polling loop: {e}")
                time.sleep(5)  # Wait before retrying

    def start_polling(self):
        """Start the data polling in a separate thread"""
        if not self.is_running:
            self.is_running = True
            self.polling_thread = threading.Thread(target=self.polling_loop, daemon=True)
            self.polling_thread.start()
            print("✅ Data polling started")
        else:
            print("⚠️  Polling is already running")

    def stop_polling(self):
        """Stop the data polling"""
        self.is_running = False
        if self.polling_thread:
            self.polling_thread.join(timeout=1)
        print("🛑 Data polling stopped")

    def add_symbol(self, symbol):
        """Add a symbol to the subscription list"""
        symbol = symbol.upper()
        if symbol not in self.subscribed_symbols:
            self.subscribed_symbols.append(symbol)
            print(f"➕ Added {symbol} to subscription list")
        else:
            print(f"⚠️  {symbol} is already subscribed")

    def remove_symbol(self, symbol):
        """Remove a symbol from the subscription list"""
        symbol = symbol.upper()
        if symbol in self.subscribed_symbols:
            self.subscribed_symbols.remove(symbol)
            print(f"➖ Removed {symbol} from subscription list")
        else:
            print(f"⚠️  {symbol} is not in subscription list")

def start_polygon_data_client():
    """Initialize and start the Polygon.io data client"""
    global polygon_ws
    polygon_ws = PolygonDataClient(POLYGON_API_KEY)
    polygon_ws.start_polling()

@socketio.on('start_market_data')
def handle_start_market_data():
    """Handle request to start market data stream"""
    try:
        start_polygon_data_client()
        emit('market_data_status', {'status': 'started', 'message': 'Market data polling started'})
    except Exception as e:
        emit('market_data_status', {'status': 'error', 'message': str(e)})

@socketio.on('stop_market_data')
def handle_stop_market_data():
    """Handle request to stop market data stream"""
    global polygon_ws
    try:
        if polygon_ws:
            polygon_ws.stop_polling()
        emit('market_data_status', {'status': 'stopped', 'message': 'Market data polling stopped'})
    except Exception as e:
        emit('market_data_status', {'status': 'error', 'message': str(e)})

@socketio.on('subscribe_symbol')
def handle_subscribe_symbol(data):
    """Handle request to subscribe to a specific symbol"""
    global polygon_ws
    try:
        symbol = data.get('symbol', '').upper()
        if not symbol:
            emit('subscription_status', {'status': 'error', 'message': 'Symbol is required'})
            return

        if polygon_ws:
            polygon_ws.add_symbol(symbol)
            emit('subscription_status', {
                'status': 'success',
                'message': f'Added {symbol} to subscription list',
                'symbol': symbol
            })
        else:
            emit('subscription_status', {'status': 'error', 'message': 'Data client not initialized'})
    except Exception as e:
        emit('subscription_status', {'status': 'error', 'message': str(e)})

@socketio.on('unsubscribe_symbol')
def handle_unsubscribe_symbol(data):
    """Handle request to unsubscribe from a specific symbol"""
    global polygon_ws
    try:
        symbol = data.get('symbol', '').upper()
        if not symbol:
            emit('subscription_status', {'status': 'error', 'message': 'Symbol is required'})
            return

        if polygon_ws:
            polygon_ws.remove_symbol(symbol)
            emit('subscription_status', {
                'status': 'success',
                'message': f'Removed {symbol} from subscription list',
                'symbol': symbol
            })
        else:
            emit('subscription_status', {'status': 'error', 'message': 'Data client not initialized'})
    except Exception as e:
        emit('subscription_status', {'status': 'error', 'message': str(e)})

@socketio.on('get_current_price')
def handle_get_current_price(data):
    """Handle request to get current price for a symbol"""
    global polygon_ws
    try:
        symbol = data.get('symbol', '').upper()
        if not symbol:
            emit('price_data', {'status': 'error', 'message': 'Symbol is required'})
            return

        if polygon_ws:
            price_data = polygon_ws.get_current_price(symbol)
            if price_data:
                emit('price_data', {'status': 'success', 'data': price_data})
            else:
                emit('price_data', {'status': 'error', 'message': f'Could not fetch price for {symbol}'})
        else:
            emit('price_data', {'status': 'error', 'message': 'Data client not initialized'})
    except Exception as e:
        emit('price_data', {'status': 'error', 'message': str(e)})

# @socketio.on('delete_message')
# def delete_message(data):
#     auth_token = data.get('Authorization')
#     message_id = data.get('message_id')
#     if not auth_token:
#         emit('delete_message', {'message': 'JWT Token Required'})
#         return
#     user_id = decode_token(f'Bearer {auth_token}')
#     if not user_id:
#         emit('delete_message', {'message': 'Invalid JWT Token'})
#         return
#     try:
#         message = ChatMessage.objects.get(pk=message_id)
#         to_user_id = message.to_user_id
#         room = str(to_user_id)
#         message.delete()
#         socketio.emit('delete_message', {
#             'status': True,
#             'message': 'Message deleted successfully',
#             'message_id': message_id,
#             'to_user_id': to_user_id
#         }, to=room)
#         emit('delete_message', {
#             'status': True,
#             'message': 'Message deleted successfully',
#             'message_id': message_id,
#             'to_user_id': to_user_id
#         })
#     except ChatMessage.DoesNotExist:
#         emit('delete_message', {'status': False,'message': 'Message not found'})

# @socketio.on('is_typing')
# def is_typing(data):
#     auth_token = data.get('Authorization')
#     to_room = data.get('to')
#     is_typing = data.get('is_typing')
#     if not auth_token:
#         emit('is_typing', {'message': 'JWT Token Required'})
#         return
#     user_id = decode_token(f'Bearer {auth_token}')
#     if not user_id:
#         emit('is_typing', {'message': 'Invalid JWT Token'})
#         return
#     room = str(to_room)
#     if is_typing == '1':
#         socketio.emit('is_typing', {'is_typing': True , 'to':to_room ,'user_id':user_id}, to=room)
#         socketio.emit('is_typing_list', {'is_typing': True , 'to':to_room ,'user_id':user_id}, to=room)
#     else:
#         socketio.emit('is_typing', {'is_typing': False, 'to':to_room ,'user_id':user_id}, to=room)
#         socketio.emit('is_typing_list', {'is_typing': False, 'to':to_room ,'user_id':user_id}, to=room)

# @socketio.on('message_read')
# def is_typing(data):
#     auth_token = data.get('Authorization')
#     to_room = data.get('to')
#     message_id = data.get('message_id')
#     if not auth_token:
#         emit('message_read', {'message': 'JWT Token Required'})
#         return
#     user_id = decode_token(f'Bearer {auth_token}')
#     if not user_id:
#         emit('message_read', {'message': 'Invalid JWT Token'})
#         return
#     if not message_id:
#         emit('message_read', {'message': 'message_id field is required'})
#         return

#     room = str(to_room)

#     try:
#         message = ChatMessage.objects.get(pk=message_id)
#         message.is_read = True
#         message.read_time = datetime.datetime.now()
#         message.save()
#         socketio.emit('message_read', {
#                       'id': message.pk, 'is_read': True, 'read_time': message.read_time}, to=room)

#     except ChatMessage.DoesNotExist:
#         emit('message_read', {'message': 'Chat message not found'})

if __name__ == '__main__':
    print("🚀 Starting Paper Trading Backend...")
    print("📊 Initializing Polygon.io data client...")

    # Start the Polygon.io data client
    start_polygon_data_client()

    print("🌐 Starting Flask-SocketIO server on port 30044...")
    socketio.run(app, allow_unsafe_werkzeug=True, debug=True, port=30044)
