# import os
# import django

# os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
# django.setup()
# from django.utils import timezone
# from Authentication.models import User
# from Project.models import *
# import base64
# import datetime
# from flask import Flask, request
# from flask_socketio import SocketIO, emit, join_room
# from flask_cors import CORS
# from flask import request


# app = Flask(__name__)
# app.config['SECRET_KEY'] = 'your_secret_key'
# app.config['UPLOAD_FOLDER'] = os.path.join('media', 'prepare_chat_files')

# CORS(app, resources={r"/*": {"origins": "*"}})  # Allow firecamp.dev
# socketio = SocketIO(app, cors_allowed_origins=["https://firecamp.dev","http://localhost:3000","http://localhost:30044","https://room8.flexioninfotech.com"], max_http_buffer_size=1024*1024*400, ping_timeout=120, ping_interval=25)



# # @socketio.on('join_socket')
# # def join_socket(data):
# #     try:
# #         auth_token = data.get('Authorization')
# #         if not auth_token:
# #             emit('join_socket', {'message': 'JWT Token Required'})
# #             return
# #         user_id = decode_token(f'Bearer {auth_token}')
# #         if not user_id:
# #             emit('join_socket', {'message': 'Invalid JWT Token'})
# #             return
# #         user_socket_id = request.sid
# #         user_data = User.objects.get(pk=user_id)
# #         user_data.socket_id = user_socket_id
# #         user_data.save()
# #         room = str(user_id)
# #         if not user_socket_id:
# #             emit('join_socket', {'message': 'User not connected'})
# #             return

# #         # Join the user to the room
# #         join_room(room)

# #         # Emit a success message to the user
# #         emit('join_socket', {'message': f'Joined room {room}'}, room=room)
# #         print(user_socket_id)
# #     except Exception as e:
# #         emit("join_socket", {'status': False, 'error': str(e)}), 500
# #         return


# # @socketio.on('send_message')
# # def send_message(data):
# #     try:
# #         auth_token = data.get('Authorization')
# #         message = data.get('message', '')
# #         type = data.get('type', 'text')
# #         message_id = data.get('message_id', '')
# #         file = data.get('file', '')
# #         to_user_id = data.get('to')
# #         if not auth_token:
# #             emit('send_message', {'message': 'JWT Token Required'})
# #             return
# #         user_id = decode_token(f'Bearer {auth_token}')
# #         if not user_id:
# #             emit('send_message', {'message': 'Invalid JWT Token'})
# #             return
# #         room = str(to_user_id)
# #         print(f'room --> {room}')
# #         if type == 'reply_message':
# #             if message_id is None or message_id == '':
# #                 emit('send_message', {
# #                      'message': 'To reply to a message Id is required'})
# #                 return
# #             else:
# #                 create = ChatMessage.objects.create(
# #                     from_user_id=user_id,
# #                     to_user_id=to_user_id,
# #                     type=type,
# #                     message_id=message_id,
# #                     messages=message
# #                 )

# #                 noti_data = {
# #                         'user_id': create.to_user.pk,
# #                         'type': 'message',
# #                         'title': 'Room8',
# #                         'message': f'{create.from_user.name} Messaged You',
# #                         'messages': message,
# #                         "from":create.from_user.pk
# #                     }
# #                 socketio.emit('receive_message', {'id': create.pk, 'message': message, 'type': type,
# #                               'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id}, to=room)
# #                 emit('send_message', {'id': create.pk, 'message': message, 'type': type,
# #                      'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id})
# #                 send_in_app_notification(
# #                     noti_data, User, socketio.emit)
# #                 return
# #         if type in ['image', 'voice', 'custom']:
# #             if file is None or file == '':
# #                 emit('send_message', {
# #                      'message': 'For type file or voice a file is required'})
# #                 return
# #             else:
# #                 file_path = None
# #                 file_path = save_base64_file(
# #                     file, user_id, datetime.datetime.now())
# #                 create = ChatMessage.objects.create(
# #                     from_user_id=user_id,
# #                     to_user_id=to_user_id,
# #                     type=type,
# #                     file=file_path,
# #                     message_id=message_id,
# #                     messages=f'Sent you an {type}'
# #                 )
# #                 noti_data = {
# #                         'user_id': create.to_user.pk,
# #                         'type': 'message',
# #                         'title': 'Room8',
# #                         'message': f'{create.from_user.name} Sent You A File',
# #                         'messages': message,
# #                         "from":create.from_user.pk
# #                     }
# #                 socketio.emit('receive_message', {'id': create.pk, 'message': file_path, 'type': type,
# #                               'file': file_path, 'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id}, to=room)
# #                 emit('send_message', {'id': create.pk, 'message': file_path, 'type': type,
# #                      'file': file_path, 'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id})
# #                 send_in_app_notification(
# #                     noti_data, User, socketio.emit)
# #                 return
# #         create = ChatMessage.objects.create(
# #             from_user_id=user_id,
# #             to_user_id=to_user_id,
# #             type=type,
# #             messages=message
# #         )
# #         noti_data = {
# #                         'user_id': create.to_user.pk,
# #                         'type': 'message',
# #                         'title': 'Room8',
# #                         'message': f'{create.from_user.name} Messaged You',
# #                         'messages': message,
# #                         "from":create.from_user.pk
# #                     }
# #         socketio.emit('receive_message', {'id': create.pk, 'message': message, 'type': type,
# #                       'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id}, to=room)

# #         emit('send_message', {'id': create.pk, 'message': message, 'type': type,
# #              'created_at': f'{timezone.localtime(create.created_at).isoformat()}', 'sent_by': user_id})
# #         send_in_app_notification(
# #                     noti_data, User, socketio.emit)
# #     except Exception as e:
# #         emit("send_message", {'status': False, 'error': str(e)}), 500
# #         return


# # @socketio.on('delete_message')
# # def delete_message(data):
# #     auth_token = data.get('Authorization')
# #     message_id = data.get('message_id')
# #     if not auth_token:
# #         emit('delete_message', {'message': 'JWT Token Required'})
# #         return
# #     user_id = decode_token(f'Bearer {auth_token}')
# #     if not user_id:
# #         emit('delete_message', {'message': 'Invalid JWT Token'})
# #         return
# #     try:
# #         message = ChatMessage.objects.get(pk=message_id)
# #         to_user_id = message.to_user_id
# #         room = str(to_user_id)
# #         message.delete()
# #         socketio.emit('delete_message', {
# #             'status': True,
# #             'message': 'Message deleted successfully',
# #             'message_id': message_id,
# #             'to_user_id': to_user_id
# #         }, to=room)
# #         emit('delete_message', {
# #             'status': True,
# #             'message': 'Message deleted successfully',
# #             'message_id': message_id,
# #             'to_user_id': to_user_id
# #         })
# #     except ChatMessage.DoesNotExist:
# #         emit('delete_message', {'status': False,'message': 'Message not found'})


# # @socketio.on('is_typing')
# # def is_typing(data):
# #     auth_token = data.get('Authorization')
# #     to_room = data.get('to')
# #     is_typing = data.get('is_typing')
# #     if not auth_token:
# #         emit('is_typing', {'message': 'JWT Token Required'})
# #         return
# #     user_id = decode_token(f'Bearer {auth_token}')
# #     if not user_id:
# #         emit('is_typing', {'message': 'Invalid JWT Token'})
# #         return
# #     room = str(to_room)
# #     if is_typing == '1':
# #         socketio.emit('is_typing', {'is_typing': True , 'to':to_room ,'user_id':user_id}, to=room)
# #         socketio.emit('is_typing_list', {'is_typing': True , 'to':to_room ,'user_id':user_id}, to=room)
# #     else:
# #         socketio.emit('is_typing', {'is_typing': False, 'to':to_room ,'user_id':user_id}, to=room)
# #         socketio.emit('is_typing_list', {'is_typing': False, 'to':to_room ,'user_id':user_id}, to=room)


# # @socketio.on('message_read')
# # def is_typing(data):
# #     auth_token = data.get('Authorization')
# #     to_room = data.get('to')
# #     message_id = data.get('message_id')
# #     if not auth_token:
# #         emit('message_read', {'message': 'JWT Token Required'})
# #         return
# #     user_id = decode_token(f'Bearer {auth_token}')
# #     if not user_id:
# #         emit('message_read', {'message': 'Invalid JWT Token'})
# #         return
# #     if not message_id:
# #         emit('message_read', {'message': 'message_id field is required'})
# #         return

# #     room = str(to_room)

# #     try:
# #         message = ChatMessage.objects.get(pk=message_id)
# #         message.is_read = True
# #         message.read_time = datetime.datetime.now()
# #         message.save()
# #         socketio.emit('message_read', {
# #                       'id': message.pk, 'is_read': True, 'read_time': message.read_time}, to=room)

# #     except ChatMessage.DoesNotExist:
# #         emit('message_read', {'message': 'Chat message not found'})


# if __name__ == '__main__':
#     socketio.run(app, allow_unsafe_werkzeug=True, debug=True, port=30044)

















import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()
from django.utils import timezone

from Project.models import *
import base64
import datetime
import websocket
import json
import threading
from flask import Flask, request
from flask_socketio import SocketIO, emit, join_room
from flask_cors import CORS
from flask import request

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key'
app.config['UPLOAD_FOLDER'] = os.path.join('media', 'prepare_chat_files')

CORS(app, resources={r"/*": {"origins": "*"}})
socketio = SocketIO(app, cors_allowed_origins=["https://firecamp.dev","http://localhost:3000","http://localhost:30044","https://room8.flexioninfotech.com"], max_http_buffer_size=1024*1024*400, ping_timeout=120, ping_interval=25)

# Polygon.io configuration - Use delayed feed for free tier
POLYGON_API_KEY = "********************************"
POLYGON_WS_URL = "wss://delayed.polygon.io/stocks"  # Use delayed feed

class PolygonWebSocket:
    def __init__(self, socketio_instance):
        self.ws = None
        self.socketio = socketio_instance
        self.subscribed_symbols = set()
        self.authenticated = False
    
    def on_message(self, ws, message):
        try:
            data = json.loads(message)
            print(f"📊 Raw message from Polygon: {data}")
            
            # Check for status messages
            for item in data:
                if item.get('ev') == 'status':
                    print(f"🔔 Status: {item.get('status')} - {item.get('message')}")
                    
                    if item.get('status') == 'auth_success':
                        self.authenticated = True
                        print("✅ Authentication successful!")
                        # Auto-subscribe to AAPL after authentication
                        self.subscribe_to_symbol('AAPL')
                        
                elif item.get('ev') == 'AM':  # Aggregate Minute data
                    print(f"📈 MINUTE DATA - Symbol: {item.get('sym')}, Price: ${item.get('c')}, Volume: {item.get('v')}")
                    
                elif item.get('ev') == 'T':   # Trade data
                    print(f"💰 TRADE - Symbol: {item.get('sym')}, Price: ${item.get('p')}, Size: {item.get('s')}")
            
            # Forward to clients
            self.socketio.emit('market_data', data)
            
        except Exception as e:
            print(f"❌ Error processing message: {e}")
    
    def on_error(self, ws, error):
        print(f"🚨 WebSocket error: {error}")
    
    def on_close(self, ws, close_status_code, close_msg):
        print("🔌 Polygon WebSocket connection closed")
        self.authenticated = False
    
    def on_open(self, ws):
        print("🚀 Connected to Polygon.io WebSocket (Delayed Feed)")
        # Authenticate immediately
        auth_message = {"action": "auth", "params": POLYGON_API_KEY}
        print
        ws.send(json.dumps(auth_message))
        print(f"🔐 Sent authentication: {auth_message}")
    
    def connect(self):
        print("🔄 Connecting to Polygon WebSocket...")
        self.ws = websocket.WebSocketApp(
            POLYGON_WS_URL,
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close
        )
        # Run in separate thread
        wst = threading.Thread(target=self.ws.run_forever)
        wst.daemon = True
        wst.start()
    
    def subscribe_to_symbol(self, symbol):
        if self.ws and self.authenticated and symbol not in self.subscribed_symbols:
            # Subscribe to Aggregate Minute data (AM) for the symbol
            subscribe_message = {
                "action": "subscribe",
                "params": f"AM.{symbol}"  # AM = Aggregate Minute
            }
            self.ws.send(json.dumps(subscribe_message))
            self.subscribed_symbols.add(symbol)
            print(f"📡 Subscribed to minute aggregates for {symbol}")
            print(f"📤 Sent subscription: {subscribe_message}")
        elif not self.authenticated:
            print("⚠️ Cannot subscribe - not authenticated yet")
        else:
            print(f"⚠️ Already subscribed to {symbol}")

# Initialize Polygon WebSocket
polygon_ws = PolygonWebSocket(socketio)

@socketio.on('connect')
def handle_connect():
    print('Client connected')
    # Connect to Polygon when first client connects
    if not polygon_ws.ws:
        polygon_ws.connect()

@socketio.on('subscribe_symbol')
def handle_subscribe(data):
    auth_token = data.get('Authorization')
    
    symbol = data.get('symbol', '').upper()
    
    if not auth_token:
        emit('subscribe_symbol', {'message': 'JWT Token Required'})
        return
    
    # user_id = decode_token(f'Bearer {auth_token}')
    # if not user_id:
    #     emit('subscribe_symbol', {'message': 'Invalid JWT Token'})
    #     return
    
    if not symbol:
        emit('subscribe_symbol', {'message': 'Symbol is required'})
        return
    
    try:
        polygon_ws.subscribe_to_symbol(symbol)
        print(f"Successfully subscribed to {symbol}")
        emit('subscribe_symbol', {
            'status': True,
            'message': f'Subscribed to {symbol}',
            'symbol': symbol
        })
    except Exception as e:
        print(f"Error subscribing to symbol: {str(e)}")
        emit('subscribe_symbol', {
            'status': False,
            'error': str(e)
        })

# @socketio.on('delete_message')
# def delete_message(data):
#     auth_token = data.get('Authorization')
#     message_id = data.get('message_id')
#     if not auth_token:
#         emit('delete_message', {'message': 'JWT Token Required'})
#         return
#     user_id = decode_token(f'Bearer {auth_token}')
#     if not user_id:
#         emit('delete_message', {'message': 'Invalid JWT Token'})
#         return
#     try:
#         message = ChatMessage.objects.get(pk=message_id)
#         to_user_id = message.to_user_id
#         room = str(to_user_id)
#         message.delete()
#         socketio.emit('delete_message', {
#             'status': True,
#             'message': 'Message deleted successfully',
#             'message_id': message_id,
#             'to_user_id': to_user_id
#         }, to=room)
#         emit('delete_message', {
#             'status': True,
#             'message': 'Message deleted successfully',
#             'message_id': message_id,
#             'to_user_id': to_user_id
#         })
#     except ChatMessage.DoesNotExist:
#         emit('delete_message', {'status': False,'message': 'Message not found'})

# @socketio.on('is_typing')
# def is_typing(data):
#     auth_token = data.get('Authorization')
#     to_room = data.get('to')
#     is_typing = data.get('is_typing')
#     if not auth_token:
#         emit('is_typing', {'message': 'JWT Token Required'})
#         return
#     user_id = decode_token(f'Bearer {auth_token}')
#     if not user_id:
#         emit('is_typing', {'message': 'Invalid JWT Token'})
#         return
#     room = str(to_room)
#     if is_typing == '1':
#         socketio.emit('is_typing', {'is_typing': True , 'to':to_room ,'user_id':user_id}, to=room)
#         socketio.emit('is_typing_list', {'is_typing': True , 'to':to_room ,'user_id':user_id}, to=room)
#     else:
#         socketio.emit('is_typing', {'is_typing': False, 'to':to_room ,'user_id':user_id}, to=room)
#         socketio.emit('is_typing_list', {'is_typing': False, 'to':to_room ,'user_id':user_id}, to=room)

# @socketio.on('message_read')
# def is_typing(data):
#     auth_token = data.get('Authorization')
#     to_room = data.get('to')
#     message_id = data.get('message_id')
#     if not auth_token:
#         emit('message_read', {'message': 'JWT Token Required'})
#         return
#     user_id = decode_token(f'Bearer {auth_token}')
#     if not user_id:
#         emit('message_read', {'message': 'Invalid JWT Token'})
#         return
#     if not message_id:
#         emit('message_read', {'message': 'message_id field is required'})
#         return

#     room = str(to_room)

#     try:
#         message = ChatMessage.objects.get(pk=message_id)
#         message.is_read = True
#         message.read_time = datetime.datetime.now()
#         message.save()
#         socketio.emit('message_read', {
#                       'id': message.pk, 'is_read': True, 'read_time': message.read_time}, to=room)

#     except ChatMessage.DoesNotExist:
#         emit('message_read', {'message': 'Chat message not found'})

if __name__ == '__main__':
    socketio.run(app, allow_unsafe_werkzeug=True, debug=True, port=30044)
